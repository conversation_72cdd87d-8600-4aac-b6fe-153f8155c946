/**
 * MoodifyMe - Main JavaScript
 * Contains common functionality used throughout the application
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Handle form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Handle mood input option selection
    const inputOptions = document.querySelectorAll('.input-option');
    const inputForms = document.querySelectorAll('.mood-input-form');

    if (inputOptions.length > 0 && inputForms.length > 0) {
        console.log('Setting up input option listeners for', inputOptions.length, 'options');
        inputOptions.forEach(option => {
            option.addEventListener('click', function() {
                console.log('Input option clicked:', this.id);

                // Remove active class from all options
                inputOptions.forEach(opt => opt.classList.remove('active'));

                // Add active class to clicked option
                this.classList.add('active');

                // Hide all input forms
                inputForms.forEach(form => form.style.display = 'none');

                // Show the corresponding input form
                const formId = this.id.replace('-option', '-form');
                console.log('Showing form:', formId);
                const targetForm = document.getElementById(formId);
                if (targetForm) {
                    targetForm.style.display = 'block';
                } else {
                    console.error('Form not found:', formId);
                }
            });
        });
    } else {
        console.log('Input options or forms not found:', {
            inputOptions: inputOptions.length,
            inputForms: inputForms.length
        });
    }

    // Handle dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');

            // Save preference to localStorage
            if (document.body.classList.contains('dark-mode')) {
                localStorage.setItem('darkMode', 'enabled');
            } else {
                localStorage.setItem('darkMode', 'disabled');
            }
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    }

    // Handle notification dismissal
    const notifications = document.querySelectorAll('.notification-item');
    if (notifications.length > 0) {
        notifications.forEach(notification => {
            const dismissBtn = notification.querySelector('.dismiss-notification');
            if (dismissBtn) {
                dismissBtn.addEventListener('click', function() {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);

                    // You can add AJAX call here to mark notification as read in the database
                });
            }
        });
    }

    // Handle recommendation feedback
    const feedbackButtons = document.querySelectorAll('.recommendation-feedback');
    if (feedbackButtons.length > 0) {
        feedbackButtons.forEach(button => {
            button.addEventListener('click', function() {
                const recommendationId = this.dataset.recommendationId;
                const feedbackType = this.dataset.feedbackType;

                // Send feedback to server via AJAX
                fetch('api/recommendation_feedback.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        recommendation_id: recommendationId,
                        feedback_type: feedbackType
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI to show feedback was received
                        this.classList.add('active');

                        // If this is a like/dislike system, toggle the other button off
                        if (feedbackType === 'like' || feedbackType === 'dislike') {
                            const oppositeType = feedbackType === 'like' ? 'dislike' : 'like';
                            const oppositeButton = document.querySelector(`.recommendation-feedback[data-recommendation-id="${recommendationId}"][data-feedback-type="${oppositeType}"]`);
                            if (oppositeButton) {
                                oppositeButton.classList.remove('active');
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
    }

    // Handle mood analysis form submission (text input)
    const moodTextForm = document.getElementById('mood-text-form');
    if (moodTextForm) {
        moodTextForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const moodText = document.getElementById('mood-text').value;
            if (!moodText.trim()) {
                alert('Please describe your mood before submitting.');
                return;
            }

            // Show loading indicator
            const submitButton = moodTextForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
            submitButton.disabled = true;

            // Send form data to server via AJAX
            const formData = new FormData(moodTextForm);

            fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show detected emotion and ask for target emotion
                    if (typeof window.showEmotionResults === 'function') {
                        window.showEmotionResults(data.emotion, data.confidence, data.emotion_id);
                    } else {
                        console.error('showEmotionResults function not found');
                        alert('Error: Could not display emotion results. Please try again.');
                    }

                    // Reset button
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                } else {
                    // Show error message
                    alert('Error analyzing mood: ' + data.message);

                    // Reset button
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while analyzing your mood. Please try again.');

                // Reset button
                submitButton.innerHTML = originalButtonText;
                submitButton.disabled = false;
            });
        });
    }

    // Handle voice input with manual text entry
    const startRecordingBtn = document.getElementById('start-recording');
    const submitVoiceBtn = document.getElementById('submit-voice');
    const recordingStatus = document.getElementById('recording-status');
    const audioDataInput = document.getElementById('audio-data');
    const voiceForm = document.getElementById('voice-form');

    console.log('Voice input elements found:', {
        startRecordingBtn: !!startRecordingBtn,
        recordingStatus: !!recordingStatus,
        submitVoiceBtn: !!submitVoiceBtn,
        audioDataInput: !!audioDataInput,
        voiceForm: !!voiceForm
    });

    if (startRecordingBtn && recordingStatus) {
        console.log('Setting up voice input event listeners...');
        let isRecording = false;

        // Simple voice input approach - user speaks and then types what they said
        startRecordingBtn.addEventListener('click', function() {
            console.log('Start recording button clicked!');
            // Show voice input prompt
            recordingStatus.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-microphone me-2"></i>Voice Input Mode</h6>
                    <p class="mb-3">Since automatic speech recognition can be unreliable, please:</p>
                    <ol class="mb-3">
                        <li><strong>Speak out loud</strong> about how you're feeling</li>
                        <li><strong>Then type</strong> what you just said in the box below</li>
                    </ol>
                    <div class="mb-3">
                        <label for="voice-text-input" class="form-label">Type what you just said:</label>
                        <textarea id="voice-text-input" class="form-control" rows="3"
                                placeholder="Example: I feel happy today, I'm excited about my new project..."
                                style="resize: vertical;"></textarea>
                    </div>
                    <button type="button" id="voice-text-submit" class="btn btn-primary">
                        <i class="fas fa-brain me-2"></i>Analyze My Voice Input
                    </button>
                </div>
            `;

            startRecordingBtn.style.display = 'none';

            // Handle the voice text submission
            const voiceTextSubmit = document.getElementById('voice-text-submit');
            const voiceTextInput = document.getElementById('voice-text-input');

            voiceTextSubmit.addEventListener('click', function() {
                const voiceText = voiceTextInput.value.trim();

                if (!voiceText) {
                    alert('Please type what you said about your feelings.');
                    return;
                }

                // Store the voice input text
                audioDataInput.value = voiceText;
                submitVoiceBtn.style.display = 'inline-block';

                recordingStatus.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check me-2"></i>Voice Input Ready!</h6>
                        <p class="mb-2"><strong>You said:</strong> "${voiceText}"</p>
                        <p class="mb-0">Click "Analyze My Mood" below to continue.</p>
                    </div>
                `;

                // Hide the voice text input area
                voiceTextSubmit.style.display = 'none';
                voiceTextInput.style.display = 'none';
            });
        });

        // Handle voice form submission
        if (voiceForm) {
            voiceForm.addEventListener('submit', function(event) {
                event.preventDefault();

                if (!audioDataInput.value) {
                    alert('Please complete the voice input process first.');
                    return;
                }

                // Show loading indicator
                submitVoiceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
                submitVoiceBtn.disabled = true;

                // Send form data to server via AJAX
                const formData = new FormData(voiceForm);

                fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show detected emotion and ask for target emotion
                        if (typeof window.showEmotionResults === 'function') {
                            window.showEmotionResults(data.emotion, data.confidence, data.emotion_id, data.needs_clarification, data.clarification_message);
                        } else {
                            console.error('showEmotionResults function not found');
                            alert('Error: Could not display emotion results. Please try again.');
                        }

                        // Reset button
                        submitVoiceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitVoiceBtn.disabled = false;
                    } else {
                        // Show error message
                        alert('Error analyzing mood: ' + data.message);

                        // Reset button
                        submitVoiceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitVoiceBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while analyzing your mood. Please try again.');

                    // Reset button
                    submitVoiceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                    submitVoiceBtn.disabled = false;
                });
            });
        }
    }

    // Handle face capture
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const captureButton = document.getElementById('capture-button');
    const submitFaceBtn = document.getElementById('submit-face');
    const imageDataInput = document.getElementById('image-data');
    const facialLoading = document.getElementById('facial-loading');
    const emotionResult = document.getElementById('emotion-result');
    const faceForm = document.getElementById('face-form');

    if (video && canvas && captureButton) {
        // Start video stream when face input is selected
        document.getElementById('face-input-option').addEventListener('click', function() {
            // Reset previous capture
            imageDataInput.value = '';
            submitFaceBtn.style.display = 'none';
            emotionResult.innerHTML = '';

            // Start video stream
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    video.srcObject = stream;
                })
                .catch(error => {
                    console.error('Error accessing camera:', error);
                    alert('Error accessing camera. Please check permissions.');
                });
        });

        captureButton.addEventListener('click', function() {
            if (video.srcObject) {
                // Draw video frame to canvas
                const context = canvas.getContext('2d');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Convert to base64 for sending to server
                const imageData = canvas.toDataURL('image/png');
                imageDataInput.value = imageData;

                // Show loading indicator
                facialLoading.style.display = 'block';
                captureButton.disabled = true;

                // In a real implementation, you would analyze the image here
                // For this example, we'll simulate a delay and show a random emotion
                setTimeout(() => {
                    facialLoading.style.display = 'none';
                    captureButton.disabled = false;

                    // Show a random emotion result
                    const emotions = Object.keys(EMOTION_CATEGORIES || {
                        'happy': 'Happy',
                        'sad': 'Sad',
                        'angry': 'Angry',
                        'anxious': 'Anxious',
                        'calm': 'Calm'
                    });
                    const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)];

                    emotionResult.innerHTML = `
                        <div class="alert alert-info">
                            <h5>Detected Emotion: <strong>${randomEmotion.charAt(0).toUpperCase() + randomEmotion.slice(1)}</strong></h5>
                            <p>Confidence: 85%</p>
                        </div>
                    `;

                    // Show submit button
                    submitFaceBtn.style.display = 'inline-block';
                }, 2000);

                // Stop video stream
                const stream = video.srcObject;
                const tracks = stream.getTracks();
                tracks.forEach(track => track.stop());
                video.srcObject = null;
            }
        });

        // Handle face form submission
        if (faceForm) {
            faceForm.addEventListener('submit', function(event) {
                event.preventDefault();

                if (!imageDataInput.value) {
                    alert('Please capture your expression first.');
                    return;
                }

                // Show loading indicator
                submitFaceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
                submitFaceBtn.disabled = true;

                // Send form data to server via AJAX
                const formData = new FormData(faceForm);

                fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show detected emotion and ask for target emotion
                        if (typeof window.showEmotionResults === 'function') {
                            window.showEmotionResults(data.emotion, data.confidence, data.emotion_id);
                        } else {
                            console.error('showEmotionResults function not found');
                            alert('Error: Could not display emotion results. Please try again.');
                        }

                        // Reset button
                        submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitFaceBtn.disabled = false;
                    } else {
                        // Show error message
                        alert('Error analyzing mood: ' + data.message);

                        // Reset button
                        submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitFaceBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while analyzing your mood. Please try again.');

                    // Reset button
                    submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                    submitFaceBtn.disabled = false;
                });
            });
        }
    }
});
